# Teacher Assistant Database Schema

## Database Overview
- **Database Type**: SQLite
- **File Location**: `~/.teacher_assistant/database.sqlite`
- **Foreign Keys**: Enabled with CASCADE and SET NULL constraints
- **Row Factory**: Dictionary-based for easy data access
- **Total Tables**: 9 tables (4 core + 5 quiz management)

## Table Summary

| Table Name | Purpose | Key Relationships | Records |
|------------|---------|-------------------|---------|
| **classes** | Class management | Parent to etudiants, matieres, quiz | Classes/Groups |
| **etudiants** | Student records + face data | Child of classes, parent to presences | Students |
| **matieres** | Subject/course management | Child of classes, parent to quiz | Subjects |
| **presences** | Attendance tracking | Child of etudiants, classes, matieres | Attendance records |
| **quiz** | Quiz metadata | Child of classes, matieres; parent to questions | Quizzes |
| **questions_quiz** | Quiz questions | Child of quiz, parent to options_quiz | Questions |
| **options_quiz** | Answer options | Child of questions_quiz | Answer choices |
| **soumissions_quiz** | Quiz submissions | Child of quiz, parent to reponses_quiz | Student submissions |
| **reponses_quiz** | Individual answers | Child of soumissions_quiz, questions_quiz | Student answers |

## Core Tables

### 1. classes
**Purpose**: Manages class information and serves as the primary organizational unit.

```sql
CREATE TABLE classes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `name`: Unique class name (e.g., "7ème A", "8ème B")
- `description`: Optional class description
- `created_at`: ISO timestamp of creation
- `updated_at`: ISO timestamp of last update

### 2. etudiants (Students)
**Purpose**: Stores student information and facial recognition encodings.

```sql
CREATE TABLE etudiants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    class_id INTEGER,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `name`: Student full name
- `code`: JSON string containing facial recognition encoding array
- `class_id`: Foreign key to classes table (nullable)
- `created_at`: ISO timestamp of enrollment
- `updated_at`: ISO timestamp of last update

### 3. matieres (Subjects)
**Purpose**: Manages subjects/courses within classes.

```sql
CREATE TABLE matieres (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    class_id INTEGER,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    UNIQUE(name, class_id)
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `name`: Subject name (e.g., "Mathématiques", "Français")
- `description`: Optional subject description
- `class_id`: Foreign key to classes table (nullable)
- `created_at`: ISO timestamp of creation
- `updated_at`: ISO timestamp of last update

**Constraints**:
- Unique combination of (name, class_id) to prevent duplicate subjects per class

### 4. presences (Attendance)
**Purpose**: Records student attendance for specific sessions.

```sql
CREATE TABLE presences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    class_id INTEGER,
    subject_id INTEGER,
    status BOOLEAN NOT NULL,
    date TEXT NOT NULL,
    time TEXT NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (student_id) REFERENCES etudiants(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (subject_id) REFERENCES matieres(id) ON DELETE SET NULL,
    UNIQUE(student_id, date, class_id, subject_id)
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `student_id`: Foreign key to etudiants table (required)
- `class_id`: Foreign key to classes table (nullable)
- `subject_id`: Foreign key to matieres table (nullable)
- `status`: Boolean (TRUE = present, FALSE = absent)
- `date`: Date in YYYY-MM-DD format
- `time`: Time in HH:MM:SS format
- `created_at`: ISO timestamp of record creation

**Constraints**:
- Unique combination prevents duplicate attendance records per student per session

## Quiz Management Tables

### 5. quiz
**Purpose**: Stores quiz metadata and configuration.

```sql
CREATE TABLE quiz (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    class_id INTEGER,
    subject_id INTEGER,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (subject_id) REFERENCES matieres(id) ON DELETE SET NULL
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `title`: Quiz title/name
- `description`: Optional quiz description
- `class_id`: Foreign key to classes table (nullable)
- `subject_id`: Foreign key to matieres table (nullable)
- `created_at`: ISO timestamp of creation
- `updated_at`: ISO timestamp of last update

### 6. questions_quiz
**Purpose**: Stores individual quiz questions.

```sql
CREATE TABLE questions_quiz (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quiz_id INTEGER NOT NULL,
    question_text TEXT NOT NULL,
    question_order INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (quiz_id) REFERENCES quiz(id) ON DELETE CASCADE
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `quiz_id`: Foreign key to quiz table (required)
- `question_text`: The question content
- `question_order`: Order/sequence of question in quiz
- `created_at`: ISO timestamp of creation

### 7. options_quiz
**Purpose**: Stores answer options for quiz questions.

```sql
CREATE TABLE options_quiz (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_id INTEGER NOT NULL,
    option_text TEXT NOT NULL,
    is_correct BOOLEAN NOT NULL,
    option_order INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (question_id) REFERENCES questions_quiz(id) ON DELETE CASCADE
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `question_id`: Foreign key to questions_quiz table (required)
- `option_text`: The answer option text
- `is_correct`: Boolean indicating if this is a correct answer
- `option_order`: Order/sequence of option within question
- `created_at`: ISO timestamp of creation

### 8. soumissions_quiz (Quiz Submissions)
**Purpose**: Records student quiz submissions and scores.

```sql
CREATE TABLE soumissions_quiz (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quiz_id INTEGER NOT NULL,
    student_name TEXT NOT NULL,
    score INTEGER NOT NULL,
    total_questions INTEGER NOT NULL,
    submitted_at TEXT NOT NULL,
    FOREIGN KEY (quiz_id) REFERENCES quiz(id) ON DELETE CASCADE
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `quiz_id`: Foreign key to quiz table (required)
- `student_name`: Name of student who submitted
- `score`: Number of correct answers
- `total_questions`: Total number of questions in quiz
- `submitted_at`: ISO timestamp of submission

### 9. reponses_quiz (Quiz Answers)
**Purpose**: Records individual student answers to quiz questions.

```sql
CREATE TABLE reponses_quiz (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    submission_id INTEGER NOT NULL,
    question_id INTEGER NOT NULL,
    selected_option_id INTEGER,
    is_correct BOOLEAN NOT NULL,
    FOREIGN KEY (submission_id) REFERENCES soumissions_quiz(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions_quiz(id) ON DELETE CASCADE,
    FOREIGN KEY (selected_option_id) REFERENCES options_quiz(id) ON DELETE SET NULL
);
```

**Columns**:
- `id`: Primary key, auto-incrementing
- `submission_id`: Foreign key to soumissions_quiz table (required)
- `question_id`: Foreign key to questions_quiz table (required)
- `selected_option_id`: Foreign key to options_quiz table (nullable)
- `is_correct`: Boolean indicating if the answer was correct

## Database Tables and Relationships

### Visual Schema Diagram

```
┌─────────────────┐
│     classes     │
│─────────────────│
│ • id (PK)       │
│ • name          │
│ • description   │
│ • created_at    │
│ • updated_at    │
└─────────────────┘
         │
         │ 1:N
         ├─────────────────────────────────────────────────────────┐
         │                                                         │
         ▼                                                         ▼
┌─────────────────┐                                       ┌─────────────────┐
│   etudiants     │                                       │    matieres     │
│─────────────────│                                       │─────────────────│
│ • id (PK)       │                                       │ • id (PK)       │
│ • name          │                                       │ • name          │
│ • code (JSON)   │                                       │ • description   │
│ • class_id (FK) │                                       │ • class_id (FK) │
│ • created_at    │                                       │ • created_at    │
│ • updated_at    │                                       │ • updated_at    │
└─────────────────┘                                       └─────────────────┘
         │                                                         │
         │ 1:N                                                     │ 1:N
         ▼                                                         ▼
┌─────────────────┐                                       ┌─────────────────┐
│    presences    │◄──────────────────────────────────────┤      quiz       │
│─────────────────│                                       │─────────────────│
│ • id (PK)       │                                       │ • id (PK)       │
│ • student_id(FK)│                                       │ • title         │
│ • class_id (FK) │                                       │ • description   │
│ • subject_id(FK)│                                       │ • class_id (FK) │
│ • status        │                                       │ • subject_id(FK)│
│ • date          │                                       │ • created_at    │
│ • time          │                                       │ • updated_at    │
│ • created_at    │                                       └─────────────────┘
└─────────────────┘                                                │
                                                                   │ 1:N
                                                                   ▼
                                                          ┌─────────────────┐
                                                          │ questions_quiz  │
                                                          │─────────────────│
                                                          │ • id (PK)       │
                                                          │ • quiz_id (FK)  │
                                                          │ • question_text │
                                                          │ • question_order│
                                                          │ • created_at    │
                                                          └─────────────────┘
                                                                   │
                                                                   │ 1:N
                                                                   ▼
                                                          ┌─────────────────┐
                                                          │  options_quiz   │
                                                          │─────────────────│
                                                          │ • id (PK)       │
                                                          │ • question_id(FK)│
                                                          │ • option_text   │
                                                          │ • is_correct    │
                                                          │ • option_order  │
                                                          │ • created_at    │
                                                          └─────────────────┘
                                                                   ▲
                                                                   │ Reference
                                                                   │
┌─────────────────┐                                       ┌─────────────────┐
│soumissions_quiz │                                       │ reponses_quiz   │
│─────────────────│                                       │─────────────────│
│ • id (PK)       │                                       │ • id (PK)       │
│ • quiz_id (FK)  │                                       │ • submission_id │
│ • student_name  │                                       │ • question_id   │
│ • score         │                                       │ • selected_opt  │
│ • total_questions│                                      │ • is_correct    │
│ • submitted_at  │                                       └─────────────────┘
└─────────────────┘
         │
         │ 1:N
         └─────────────────────────────────────────────────────────┘
```

### Relationship Types

#### **Core Entity Relationships**
- **classes** → **etudiants** (1:N) - One class has many students
- **classes** → **matieres** (1:N) - One class has many subjects
- **etudiants** → **presences** (1:N) - One student has many attendance records
- **classes** → **quiz** (1:N) - One class has many quizzes
- **matieres** → **quiz** (1:N) - One subject has many quizzes

#### **Quiz System Relationships**
- **quiz** → **questions_quiz** (1:N) - One quiz has many questions
- **questions_quiz** → **options_quiz** (1:N) - One question has many options
- **quiz** → **soumissions_quiz** (1:N) - One quiz has many submissions
- **soumissions_quiz** → **reponses_quiz** (1:N) - One submission has many answers

#### **Cross-Reference Relationships**
- **presences** references **classes**, **matieres**, **etudiants**
- **reponses_quiz** references **options_quiz** for selected answers

### Relationship Matrix

| Parent Table | Child Table | Relationship Type | Foreign Key | Constraint |
|--------------|-------------|-------------------|-------------|------------|
| **classes** | **etudiants** | 1:N | class_id | SET NULL |
| **classes** | **matieres** | 1:N | class_id | SET NULL |
| **classes** | **presences** | 1:N | class_id | SET NULL |
| **classes** | **quiz** | 1:N | class_id | SET NULL |
| **etudiants** | **presences** | 1:N | student_id | CASCADE |
| **matieres** | **presences** | 1:N | subject_id | SET NULL |
| **matieres** | **quiz** | 1:N | subject_id | SET NULL |
| **quiz** | **questions_quiz** | 1:N | quiz_id | CASCADE |
| **quiz** | **soumissions_quiz** | 1:N | quiz_id | CASCADE |
| **questions_quiz** | **options_quiz** | 1:N | question_id | CASCADE |
| **questions_quiz** | **reponses_quiz** | 1:N | question_id | CASCADE |
| **soumissions_quiz** | **reponses_quiz** | 1:N | submission_id | CASCADE |
| **options_quiz** | **reponses_quiz** | 1:1 | selected_option_id | SET NULL |

### Data Flow Patterns

#### **Student Management Flow**
```
classes → etudiants → presences
```

#### **Academic Structure Flow**
```
classes → matieres → quiz → questions_quiz → options_quiz
```

#### **Quiz Submission Flow**
```
quiz → soumissions_quiz → reponses_quiz → options_quiz (reference)
```

#### **Attendance Tracking Flow**
```
etudiants + classes + matieres → presences
```

### Foreign Key Constraints
- **CASCADE DELETE**: Child records deleted when parent is deleted
  - etudiants → presences
  - quiz → questions_quiz → options_quiz
  - quiz → soumissions_quiz → reponses_quiz

- **SET NULL**: Foreign key set to NULL when parent is deleted
  - classes → etudiants, matieres, presences, quiz
  - matieres → presences, quiz
  - options_quiz → reponses_quiz

## Data Types and Formats

### Text Fields
- **Names/Titles**: UTF-8 text, no length limit
- **Descriptions**: UTF-8 text, optional, no length limit
- **JSON Data**: Face encodings stored as JSON strings in `code` field

### Timestamps
- **Format**: ISO 8601 format (YYYY-MM-DDTHH:MM:SS.ffffff)
- **Timezone**: Local timezone
- **Fields**: created_at, updated_at, submitted_at

### Date/Time Fields
- **Date**: YYYY-MM-DD format
- **Time**: HH:MM:SS format

### Boolean Fields
- **Storage**: INTEGER (0 = FALSE, 1 = TRUE)
- **Usage**: status, is_correct

## Database Initialization
The database is initialized in two phases:
1. **Core tables**: classes, etudiants, matieres, presences
2. **Quiz tables**: quiz, questions_quiz, options_quiz, soumissions_quiz, reponses_quiz
