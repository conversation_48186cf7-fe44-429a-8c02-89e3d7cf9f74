# Teacher Assistant - Project Structure

This document describes the organized file structure of the Teacher Assistant project.

## Directory Structure

```
teacher_assistant/
├── main.py                          # Main application entry point
├── pyproject.toml                   # Project configuration and dependencies
├── README.md                        # Project documentation
├── STRUCTURE.md                     # This file - project structure documentation
├── database.sqlite                  # SQLite database file
├── .gitignore                       # Git ignore rules
│
├── gui/                             # User interface components
│   ├── app.py                       # Main GUI application
│   ├── components/                  # Reusable UI components
│   ├── views/                       # Application views/pages
│   ├── services/                    # Business logic services
│   ├── config/                      # Configuration files
│   ├── state/                       # Application state management
│   └── utils/                       # GUI utilities
│
├── facial_recognition_system/       # Face recognition core functionality
│   ├── database.py                  # Database interface
│   ├── local_database.py            # Local SQLite database operations
│   ├── face_processing.py           # Face detection and recognition
│   └── config.py                    # System configuration
│
├── quiz_management/                 # Quiz system functionality
│   ├── services/                    # Quiz services
│   │   ├── quiz_service.py          # Quiz CRUD operations
│   │   └── quiz_taking_service.py   # Quiz taking web interface
│   └── __init__.py
│
├── hardware/                        # Hardware-related components
│   ├── __init__.py                  # Hardware module initialization
│   ├── JetsonCamera.py              # CSI camera interface for Jetson Nano
│   ├── Focuser.py                   # Camera focus control
│   ├── FocuserExample.py            # Focus control example/demo
│   └── camera_diagnostic.py         # Camera troubleshooting tool
│
├── tools/                           # Utility scripts and tools
│   ├── __init__.py                  # Tools module initialization
│   ├── clear_database.py            # Database cleanup utility
│   └── test_attendance_performance.py # Performance testing tool
│
├── assets/                          # Static assets (images, fonts, etc.)
├── fonts/                           # Font files
├── documentation/                   # Project documentation
└── student_uploads/                 # Student enrollment uploads
```

## Key Components

### Main Application
- **main.py**: Entry point that starts the GUI application and face display

### Hardware Module (`hardware/`)
- **JetsonCamera.py**: Real camera implementation for Jetson Nano with CSI camera support
- **Focuser.py**: Camera focus control for Arducam lenses
- **camera_diagnostic.py**: Diagnostic tool for troubleshooting camera issues

### Tools Module (`tools/`)
- **clear_database.py**: Utility to clear all data from the database
- **test_attendance_performance.py**: Performance testing for attendance queries

### GUI Module (`gui/`)
- Organized into components, views, services, and utilities
- Handles all user interface functionality

### Facial Recognition System (`facial_recognition_system/`)
- Core face recognition and database functionality
- Handles student enrollment and attendance tracking

### Quiz Management (`quiz_management/`)
- Quiz creation, management, and taking functionality
- Web-based quiz interface for students

## Import Structure

### Hardware Components
```python
# Import specific camera classes
from hardware.JetsonCamera import Camera as JetsonCamera
from hardware.Focuser import Focuser

# Import default camera (always uses real Jetson camera)
from hardware import Camera
```

### Tools
```python
# Tools are meant to be run as scripts, not imported
# Run from project root:
python3 tools/clear_database.py
python3 tools/test_attendance_performance.py
```

## File Organization Benefits

1. **Clear Separation**: Hardware, tools, and core functionality are clearly separated
2. **Easy Maintenance**: Related files are grouped together
3. **Better Imports**: Cleaner import structure with proper module organization
4. **Reduced Clutter**: Main directory only contains essential files
5. **Scalability**: Easy to add new components in appropriate directories

## Development Guidelines

- **Hardware components**: Add new camera or sensor interfaces to `hardware/`
- **Utility scripts**: Add new tools and utilities to `tools/`
- **Core functionality**: Keep in existing modules (`gui/`, `facial_recognition_system/`, `quiz_management/`)
- **Documentation**: Update this file when adding new major components

## Running the Application

From the project root directory:
```bash
python3 main.py
```

All imports and functionality remain the same for end users.
