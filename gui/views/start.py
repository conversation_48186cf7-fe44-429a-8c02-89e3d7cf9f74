import flet as ft
from gui.config.constants import ROUTE_DASHBOARD
from gui.config.language import get_text

def create_start_view(page: ft.Page):
    """Create the start page with app description, logo, and creator info."""
    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'fr')
    
    # Login form fields with validation
    username_field = ft.TextField(
        label="Username",
        width=300,
        border_radius=8,
        text_align=ft.TextAlign.LEFT,
        bgcolor=ft.colors.WHITE,
        hint_text="Enter your username",
        helper_text="Required field"
    )
    
    password_field = ft.TextField(
        label="Password",
        width=300,
        password=True,
        can_reveal_password=True,
        border_radius=8,
        text_align=ft.TextAlign.LEFT,
        bgcolor=ft.colors.WHITE,
        hint_text="Enter your password",
        helper_text="Required field"
    )
    
    def validate_login(_):
        # Reset error states
        username_field.error_text = None
        password_field.error_text = None
        has_error = False

        # Validate required fields
        if not username_field.value:
            username_field.error_text = get_text("username_required", current_language)
            has_error = True
        elif username_field.value != page.app_state.username:
            username_field.error_text = get_text("invalid_username", current_language)
            has_error = True

        # Validate password
        if not password_field.value:
            password_field.error_text = get_text("password_required", current_language)
            has_error = True
        elif not page.app_state.verify_password(password_field.value):
            password_field.error_text = get_text("invalid_password", current_language)
            password_field.value = ""
            has_error = True

        if not has_error:
            page.go(ROUTE_DASHBOARD)
        
        page.update()

    # App logo/icon (using a built-in icon as placeholder for Ministry logo)
    logo_container = ft.Container(
        content=ft.Icon(
            ft.Icons.SCHOOL,
            size=80,
            color=ft.Colors.BLUE_600
        ),
        margin=ft.margin.only(bottom=20)
    )

    # Ministry of Education Tunisia text
    ministry_text = ft.Text(
        get_text("ministry_education_tunisia", current_language),
        size=16,
        weight=ft.FontWeight.W_500,
        color=ft.Colors.BLUE_800,
        text_align=ft.TextAlign.CENTER
    )

    # App title
    app_title = ft.Text(
        get_text("app_name", current_language),
        size=36,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.BLUE_900,
        text_align=ft.TextAlign.CENTER
    )

    # App description
    app_description = ft.Text(
        get_text("educational_management_system", current_language),
        size=16,
        color=ft.Colors.BLUE_GREY_200,
        text_align=ft.TextAlign.CENTER,
        width=500 if not is_mobile else page.width * 0.9
    )

    # Login message
    login_message = ft.Text(
        "Please login to continue",
        size=16,
        color=ft.Colors.GREY_700,
        text_align=ft.TextAlign.CENTER
    )



    # Main content container
    # Login form container
    login_form = ft.Container(
        content=ft.Column([
            ft.Text("Login", size=24, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE_900),
            ft.Container(height=20),
            username_field,
            ft.Container(height=10),
            password_field,
            ft.Container(height=20),
            ft.ElevatedButton(
                text="Login",
                width=300,
                style=ft.ButtonStyle(
                    color=ft.colors.WHITE,
                    bgcolor=ft.colors.BLUE_600,
                    padding=ft.padding.symmetric(horizontal=16, vertical=12),
                ),
                on_click=validate_login
            )
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0),
        padding=30,
        bgcolor=ft.colors.WHITE,
        border_radius=12,
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=15,
            color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
            offset=ft.Offset(0, 4)
        )
    )

    main_content = ft.Container(
        content=ft.Column([
            logo_container,
            ministry_text,
            ft.Container(height=20),
            app_title,
            ft.Container(height=20),
            app_description,
            ft.Container(height=40),
            login_form,
            ft.Container(height=60),
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=10),
        padding=ft.padding.all(40),
        alignment=ft.alignment.center,
        expand=True
    )

    # Gradient background container
    background_container = ft.Container(
        content=main_content,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[ft.Colors.BLUE_50, ft.Colors.WHITE, ft.Colors.BLUE_50]
        ),
        expand=True
    )

    # Create the view
    view = ft.View(
        route="/",
        controls=[background_container],
        padding=0,
        spacing=0
    )

    return view
