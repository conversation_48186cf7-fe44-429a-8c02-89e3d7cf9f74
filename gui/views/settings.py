"""
Settings view for the application.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.config.language import get_text, LANGUAGE_NAMES, DEFAULT_LANGUAGE

def create_settings_view(page: ft.Page):
    """
    Create the settings view with modern, beautiful design.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The settings view
    """
    # Get current language
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    # Create language dropdown with modern styling
    language_dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(key=lang_code, text=name)
            for lang_code, name in LANGUAGE_NAMES.items()
        ],
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.BLUE_200,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
    )

    # Current credentials display
    current_username_display = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.PERSON, color=ft.colors.BLUE_600, size=20),
            ft.Text(
                f"{get_text('username', current_language)}: {page.app_state.username}",
                size=14,
                weight=ft.FontWeight.W_500,
                color=ft.colors.GREY_700
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.BLUE_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.BLUE_200),
    )

    # Credentials fields with modern styling
    current_password_field = ft.TextField(
        label=get_text("current_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("current_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    new_username_field = ft.TextField(
        label=get_text("new_username", current_language),
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("new_username", current_language),
        prefix_icon=ft.icons.PERSON_OUTLINE,
    )

    new_password_field = ft.TextField(
        label=get_text("new_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("new_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    confirm_password_field = ft.TextField(
        label=get_text("confirm_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("confirm_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    # Success message container
    success_message = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN_600, size=20),
            ft.Text(
                get_text("credentials_updated", current_language),
                color=ft.colors.GREEN_600,
                weight=ft.FontWeight.W_500
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.GREEN_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.GREEN_200),
        visible=False
    )

    # Save settings function with enhanced feedback
    def save_settings(_):
        # Reset error states and hide success message
        current_password_field.error_text = None
        new_username_field.error_text = None
        new_password_field.error_text = None
        confirm_password_field.error_text = None
        success_message.visible = False
        has_error = False

        # Get selected language
        selected_language = language_dropdown.value

        # Update language if changed
        if selected_language != current_language:
            if hasattr(page, 'app_state'):
                page.app_state.set_language(selected_language)
            else:
                page.language = selected_language

        # Validate credentials change if any field is filled
        if (current_password_field.value or new_username_field.value or
            new_password_field.value or confirm_password_field.value):

            # Verify current password
            if not current_password_field.value:
                current_password_field.error_text = get_text("password_required", current_language)
                has_error = True
            elif not page.app_state.verify_password(current_password_field.value):
                current_password_field.error_text = get_text("invalid_current_password", current_language)
                has_error = True

            # Validate new username if provided
            if new_username_field.value and len(new_username_field.value) < 3:
                new_username_field.error_text = get_text("username_too_short", current_language)
                has_error = True

            # Validate new password if provided
            if new_password_field.value:
                if len(new_password_field.value) < 6:
                    new_password_field.error_text = get_text("password_too_short", current_language)
                    has_error = True
                elif new_password_field.value != confirm_password_field.value:
                    confirm_password_field.error_text = get_text("passwords_dont_match", current_language)
                    has_error = True

            # If all validations pass, update credentials
            if not has_error:
                try:
                    # Update credentials in one transaction if both are changed
                    if new_username_field.value and new_password_field.value:
                        success = page.app_state.update_credentials(
                            new_username=new_username_field.value,
                            new_password=new_password_field.value
                        )
                    # Update only username
                    elif new_username_field.value:
                        success = page.app_state.update_credentials(new_username=new_username_field.value)
                    # Update only password
                    elif new_password_field.value:
                        success = page.app_state.update_credentials(new_password=new_password_field.value)
                    else:
                        success = False

                    if success:
                        # Clear credential fields after successful update
                        current_password_field.value = ""
                        new_username_field.value = ""
                        new_password_field.value = ""
                        confirm_password_field.value = ""

                        # Update current username display
                        current_username_display.content.controls[1].value = f"{get_text('username', current_language)}: {page.app_state.username}"

                        # Show success message
                        success_message.visible = True

                        # Also show snackbar for better feedback
                        page.show_snack_bar(
                            ft.SnackBar(
                                content=ft.Text(get_text("credentials_updated", current_language)),
                                bgcolor=ft.colors.GREEN_600
                            )
                        )
                    else:
                        current_password_field.error_text = "Failed to update credentials"

                except Exception as e:
                    # Show error if database update fails
                    current_password_field.error_text = f"Database error: {str(e)}"

        page.update()

    # Create modern settings content with card-based design
    settings_content = ft.Column([
        # Header section
        ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.SETTINGS, color=ft.colors.BLUE_600, size=28),
                    ft.Text(
                        get_text("settings", current_language),
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.BLUE_900
                    )
                ], spacing=12),
                ft.Container(height=8),
                ft.Text(
                    get_text("settings_description", current_language),
                    size=14,
                    color=ft.colors.GREY_600
                )
            ]),
            padding=ft.padding.all(24),
            margin=ft.margin.only(bottom=20),
        ),

        # Language section with modern card design
        ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.LANGUAGE, color=ft.colors.BLUE_600, size=20),
                    ft.Text(
                        get_text("language", current_language),
                        size=18,
                        weight=ft.FontWeight.W_600,
                        color=ft.colors.GREY_800
                    )
                ], spacing=8),
                ft.Container(height=16),
                language_dropdown,
            ]),
            padding=ft.padding.all(24),
            margin=ft.margin.only(bottom=20),
            bgcolor=ft.colors.WHITE,
            border_radius=16,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
        ),

        # Current credentials display section
        ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.ACCOUNT_CIRCLE, color=ft.colors.BLUE_600, size=20),
                    ft.Text(
                        get_text("current_account", current_language),
                        size=18,
                        weight=ft.FontWeight.W_600,
                        color=ft.colors.GREY_800
                    )
                ], spacing=8),
                ft.Container(height=16),
                current_username_display,
            ]),
            padding=ft.padding.all(24),
            margin=ft.margin.only(bottom=20),
            bgcolor=ft.colors.WHITE,
            border_radius=16,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
        ),

        # Credentials change section with modern card design
        ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.SECURITY, color=ft.colors.BLUE_600, size=20),
                    ft.Text(
                        get_text("change_credentials", current_language),
                        size=18,
                        weight=ft.FontWeight.W_600,
                        color=ft.colors.GREY_800
                    )
                ], spacing=8),
                ft.Container(height=16),
                current_password_field,
                ft.Container(height=16),
                new_username_field,
                ft.Container(height=16),
                new_password_field,
                ft.Container(height=16),
                confirm_password_field,
                ft.Container(height=20),
                success_message,
            ]),
            padding=ft.padding.all(24),
            margin=ft.margin.only(bottom=20),
            bgcolor=ft.colors.WHITE,
            border_radius=16,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
        ),

        # Action buttons section
        ft.Container(
            content=ft.Row([
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Icon(ft.icons.SAVE, size=18),
                        ft.Text(get_text("save", current_language), size=14, weight=ft.FontWeight.W_500)
                    ], spacing=8, tight=True),
                    on_click=save_settings,
                    width=160,
                    height=48,
                    style=ft.ButtonStyle(
                        bgcolor=ft.colors.BLUE_600,
                        color=ft.colors.WHITE,
                        shape=ft.RoundedRectangleBorder(radius=12),
                        elevation=2,
                    ),
                ),
                ft.Container(width=16),
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.icons.REFRESH, size=18, color=ft.colors.GREY_600),
                        ft.Text(get_text("reset", current_language), size=14, weight=ft.FontWeight.W_500, color=ft.colors.GREY_600)
                    ], spacing=8, tight=True),
                    on_click=lambda _: reset_form(),
                    width=160,
                    height=48,
                    style=ft.ButtonStyle(
                        side=ft.BorderSide(1, ft.colors.GREY_300),
                        shape=ft.RoundedRectangleBorder(radius=12),
                    ),
                ),
            ], alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.all(24),
        ),
    ],
    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
    spacing=0,
    scroll=ft.ScrollMode.AUTO)

    # Reset form function
    def reset_form():
        current_password_field.value = ""
        new_username_field.value = ""
        new_password_field.value = ""
        confirm_password_field.value = ""
        current_password_field.error_text = None
        new_username_field.error_text = None
        new_password_field.error_text = None
        confirm_password_field.error_text = None
        success_message.visible = False
        page.update()

    # Create the settings view
    return create_page_layout(
        page,
        get_text("settings", current_language),
        settings_content
    )
