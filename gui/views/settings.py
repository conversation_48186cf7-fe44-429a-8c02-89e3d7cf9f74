"""
Settings view for the application.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.config.language import get_text, LANGUAGE_NAMES, DEFAULT_LANGUAGE

def create_settings_view(page: ft.Page):
    """
    Create the settings view.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The settings view
    """
    # Get current language
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    # Create language dropdown
    language_dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(key=lang_code, text=name)
            for lang_code, name in LANGUAGE_NAMES.items()
        ],
        width=250
    )

    # Theme switch removed as requested

    # Credentials fields
    current_password_field = ft.TextField(
        label=get_text("current_password", current_language),
        password=True,
        can_reveal_password=True,
        width=250,
        border_radius=8,
    )

    new_username_field = ft.TextField(
        label=get_text("new_username", current_language),
        width=250,
        border_radius=8,
    )

    new_password_field = ft.TextField(
        label=get_text("new_password", current_language),
        password=True,
        can_reveal_password=True,
        width=250,
        border_radius=8,
    )

    confirm_password_field = ft.TextField(
        label=get_text("confirm_password", current_language),
        password=True,
        can_reveal_password=True,
        width=250,
        border_radius=8,
    )

    # Save settings function
    def save_settings(_):
        # Reset error states
        current_password_field.error_text = None
        new_username_field.error_text = None
        new_password_field.error_text = None
        confirm_password_field.error_text = None
        has_error = False

        # Get selected language
        selected_language = language_dropdown.value
        
        # Update language if changed
        if selected_language != current_language:
            if hasattr(page, 'app_state'):
                page.app_state.set_language(selected_language)
            else:
                page.language = selected_language

        # Validate credentials change if any field is filled
        if (current_password_field.value or new_username_field.value or 
            new_password_field.value or confirm_password_field.value):
            
            # Verify current password
            if not page.app_state.verify_password(current_password_field.value):
                current_password_field.error_text = get_text("invalid_current_password", current_language)
                has_error = True
            
            # Validate new username if provided
            if new_username_field.value and len(new_username_field.value) < 3:
                new_username_field.error_text = get_text("username_too_short", current_language)
                has_error = True

            # Validate new password if provided
            if new_password_field.value:
                if len(new_password_field.value) < 6:
                    new_password_field.error_text = get_text("password_too_short", current_language)
                    has_error = True
                elif new_password_field.value != confirm_password_field.value:
                    confirm_password_field.error_text = get_text("passwords_dont_match", current_language)
                    has_error = True

            # If all validations pass, update credentials
            if not has_error and current_password_field.value:
                try:
                    # Update credentials in one transaction if both are changed
                    if new_username_field.value and new_password_field.value:
                        page.app_state.update_credentials(
                            new_username=new_username_field.value,
                            new_password=new_password_field.value
                        )
                    # Update only username
                    elif new_username_field.value:
                        page.app_state.update_credentials(new_username=new_username_field.value)
                    # Update only password
                    elif new_password_field.value:
                        page.app_state.update_credentials(new_password=new_password_field.value)
                    
                    # Clear credential fields after successful update
                    current_password_field.value = ""
                    new_username_field.value = ""
                    new_password_field.value = ""
                    confirm_password_field.value = ""
                    
                    # Show success message
                    page.show_snack_bar(
                        ft.SnackBar(
                            content=ft.Text(get_text("credentials_updated", current_language)),
                            bgcolor=ft.colors.GREEN_600
                        )
                    )
                except Exception as e:
                    # Show error if database update fails
                    current_password_field.error_text = str(e)

    # Create settings content
    settings_content = ft.Column([
        # Language section
        ft.Container(
            content=ft.Column([
                ft.Text(
                    get_text("language", current_language),
                    size=18,
                    weight=ft.FontWeight.BOLD
                ),
                language_dropdown,
            ]),
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=10),
            border_radius=10,
            border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
        ),
        
        # Credentials section
        ft.Container(
            content=ft.Column([
                ft.Text(
                    get_text("change_credentials", current_language),
                    size=18,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Container(height=10),
                current_password_field,
                ft.Container(height=10),
                new_username_field,
                ft.Container(height=10),
                new_password_field,
                ft.Container(height=10),
                confirm_password_field,
            ]),
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=10),
            border_radius=10,
            border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
        ),

        # Save button
        ft.ElevatedButton(
            text=get_text("save", current_language),
            on_click=save_settings,
            width=200,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
            ),
        ),
    ],
    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
    spacing=10)

    # Create the settings view
    return create_page_layout(
        page,
        get_text("settings", current_language),
        settings_content
    )
